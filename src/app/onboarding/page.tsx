"use client";

import {
  Activity,
  Arrow<PERSON>eft,
  ArrowRight,
  CheckCircle2,
  Info,
  Ruler,
  Target,
  TrendingUp,
  User,
  XCircle,
} from "lucide-react";
import { type ChangeEvent, type JSX, useMemo, useState } from "react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Type definitions
type Gender = "male" | "female" | "other";
type ActivityLevel = "sedentary" | "lightly_active" | "moderately_active" | "very_active";
type StepNumber = 1 | 2 | 3 | 4;

interface FormData {
  name: string;
  age: string;
  gender: Gender | "";
  height: string;
  weight: string;
  bodyFat?: string;
  waist?: string;
  hip?: string;
  targetWeight: string;
  targetBodyFat?: string;
  targetWaist?: string;
  targetHip?: string;
  timeline: string;
  activityLevel: ActivityLevel | "";
}

interface FormErrors {
  [key: string]: string | undefined;
}

interface BMICategory {
  label: string;
  color: string;
}

const Onboarding = () => {
  const [step, setStep] = useState<StepNumber>(1);
  const [loading, setLoading] = useState<boolean>(false);
  const [errors, setErrors] = useState<FormErrors>({});
  const [formData, setFormData] = useState<FormData>({
    activityLevel: "",
    age: "",
    gender: "",
    height: "",
    name: "",
    targetWeight: "",
    timeline: "",
    weight: "",
  });

  const progress: number = useMemo(() => ((step - 1) / 3) * 100, [step]);

  const validationSchema: Record<
    StepNumber,
    Partial<Record<keyof FormData, (value: string) => string | undefined>>
  > = {
    1: {
      age: (value) => {
        const ageNum = parseInt(value, 10);
        return Number.isNaN(ageNum) || ageNum < 13 || ageNum > 120
          ? "Please enter a valid age (13-120)"
          : undefined;
      },
      gender: (value) => (value ? undefined : "Please select your gender"),
      height: (value) => {
        const heightNum = parseFloat(value);
        return Number.isNaN(heightNum) || heightNum < 100 || heightNum > 250
          ? "Please enter a valid height (100-250 cm)"
          : undefined;
      },
      name: (value) => (value.trim().length < 2 ? "Name must be at least 2 characters" : undefined),
    },
    2: {
      weight: (value) => {
        const weightNum = parseFloat(value);
        return Number.isNaN(weightNum) || weightNum < 30 || weightNum > 300
          ? "Please enter a valid weight (30-300 kg)"
          : undefined;
      },
    },
    3: {
      activityLevel: (value) => (value ? undefined : "Please select your activity level"),
      targetWeight: (value) => {
        const targetWeightNum = parseFloat(value);
        return Number.isNaN(targetWeightNum) || targetWeightNum < 30 || targetWeightNum > 300
          ? "Please enter a valid target weight"
          : undefined;
      },
      timeline: (value) => {
        const timelineNum = parseInt(value, 10);
        return Number.isNaN(timelineNum) || timelineNum < 7 || timelineNum > 365
          ? "Timeline should be between 7-365 days"
          : undefined;
      },
    },
    4: {},
  };

  const validateStep = (currentStep: StepNumber): boolean => {
    const newErrors: FormErrors = {};
    const schema = validationSchema[currentStep];

    for (const field in schema) {
      const key = field as keyof FormData;
      const rule = schema[key];
      if (rule) {
        const error = rule(formData[key] as string);
        if (error) {
          newErrors[key] = error;
        }
      }
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setFormData((prev) => ({ ...prev, [id]: value }));
  };

  const handleSelectChange = (id: keyof FormData, value: string) => {
    setFormData((prev) => ({ ...prev, [id]: value }));
  };

  const calculateBMI = (): string => {
    const heightM = parseFloat(formData.height) / 100;
    const weightKg = parseFloat(formData.weight);
    if (!heightM || !weightKg) return "0";
    return (weightKg / (heightM * heightM)).toFixed(1);
  };

  const getBMICategory = (bmi: number): BMICategory => {
    if (bmi < 18.5) return { color: "text-blue-600", label: "Underweight" };
    if (bmi < 25) return { color: "text-green-600", label: "Normal" };
    if (bmi < 30) return { color: "text-yellow-600", label: "Overweight" };
    return { color: "text-red-600", label: "Obese" };
  };

  const calculateCalories = (): number => {
    const { weight, height, age, gender, activityLevel } = formData;
    if (!weight || !height || !age || !gender || !activityLevel) return 0;

    const weightKg = parseFloat(weight);
    const heightCm = parseFloat(height);
    const ageYears = parseInt(age, 10);

    const bmr =
      gender === "male"
        ? 88.362 + 13.397 * weightKg + 4.799 * heightCm - 5.677 * ageYears
        : 447.593 + 9.247 * weightKg + 3.098 * heightCm - 4.33 * ageYears;

    const multipliers: Record<ActivityLevel, number> = {
      lightly_active: 1.375,
      moderately_active: 1.55,
      sedentary: 1.2,
      very_active: 1.725,
    };
    return Math.round(bmr * multipliers[activityLevel]);
  };

  const isStepValid = useMemo(() => {
    const schema = validationSchema[step];
    for (const field in schema) {
      if (!formData[field as keyof FormData]) return false;
    }
    return validateStep(step);
  }, [formData, step]);

  const handleNext = (): void => {
    if (validateStep(step)) {
      setStep((prev) => (prev + 1) as StepNumber);
      window.scrollTo({ behavior: "smooth", top: 0 });
    }
  };

  const handleBack = (): void => {
    setStep((prev) => (prev - 1) as StepNumber);
    window.scrollTo({ behavior: "smooth", top: 0 });
  };

  const handleSubmit = async (): Promise<void> => {
    if (!validateStep(3)) return;
    setLoading(true);
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1500));
    setLoading(false);
    setStep(4);
  };

  const stepIcons: Record<StepNumber, JSX.Element> = {
    1: <User className="h-5 w-5" />,
    2: <Ruler className="h-5 w-5" />,
    3: <Target className="h-5 w-5" />,
    4: <CheckCircle2 className="h-5 w-5" />,
  };

  const stepTitles: Record<StepNumber, string> = {
    1: "Personal Details",
    2: "Current Measurements",
    3: "Your Goals",
    4: "Setup Complete",
  };

  return (
    <div className="bg-gradient-to-br from-primary/5 via-secondary/5 to-accent/5 flex min-h-screen items-center justify-center px-4 py-8">
      <Card className="w-full max-w-3xl border-2 shadow-2xl">
        <CardHeader className="space-y-4 pb-8">
          {step <= 3 && (
            <>
              <div className="mb-2 flex items-center justify-between">
                {([1, 2, 3] as const).map((stepNum) => (
                  <div className="flex flex-1 items-center" key={stepNum}>
                    <div
                      className={`flex h-12 w-12 items-center justify-center rounded-full border-2 transition-all duration-300 ${
                        stepNum < step
                          ? "border-primary bg-primary text-primary-foreground"
                          : stepNum === step
                            ? "border-primary bg-primary text-primary-foreground ring-4 ring-primary/20"
                            : "border-muted bg-background text-muted-foreground"
                      }`}
                    >
                      {stepNum < step ? <CheckCircle2 className="h-6 w-6" /> : stepIcons[stepNum]}
                    </div>
                    {stepNum < 3 && (
                      <div
                        className={`mx-2 h-1 flex-1 rounded transition-all duration-300 ${
                          stepNum < step ? "bg-primary" : "bg-muted"
                        }`}
                      />
                    )}
                  </div>
                ))}
              </div>

              <Progress className="h-2.5" value={progress} />
            </>
          )}

          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-3xl font-bold text-transparent">
                {stepTitles[step]}
              </CardTitle>
              <CardDescription className="mt-2 text-base">
                {step <= 3 ? `Step ${step} of 3` : "Congratulations!"}
              </CardDescription>
            </div>
            <Badge className="px-3 py-1 text-sm" variant="outline">
              {step === 1 && "Basic Info"}
              {step === 2 && "Health Metrics"}
              {step === 3 && "Target Setting"}
              {step === 4 && "Success"}
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="pb-8">
          {step === 1 && (
            <div className="space-y-6 animate-in fade-in slide-in-from-right-4 duration-300">
              <Alert className="border-primary/20 bg-primary/5">
                <Info className="h-4 w-4 text-primary" />
                <AlertDescription className="text-sm">
                  This information helps us personalize your fitness journey
                </AlertDescription>
              </Alert>

              <div className="space-y-2">
                <Label className="text-base font-semibold" htmlFor="name">
                  Full Name <span className="text-destructive">*</span>
                </Label>
                <Input
                  aria-describedby={errors.name ? "name-error" : undefined}
                  aria-invalid={!!errors.name}
                  aria-required="true"
                  className={`h-12 text-base ${
                    errors.name
                      ? "border-destructive focus-visible:ring-destructive"
                      : "border-muted"
                  }`}
                  id="name"
                  onChange={handleInputChange}
                  placeholder="e.g., John Doe"
                  value={formData.name}
                />
                {errors.name && (
                  <p
                    className="text-sm text-destructive flex items-center gap-1 animate-in fade-in slide-in-from-top-1"
                    id="name-error"
                  >
                    <XCircle className="w-3 h-3" />
                    {errors.name}
                  </p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label className="text-base font-semibold" htmlFor="age">
                    Age <span className="text-destructive">*</span>
                  </Label>
                  <Input
                    aria-invalid={!!errors.age}
                    aria-required="true"
                    className={`h-12 text-base ${errors.age ? "border-destructive" : ""}`}
                    id="age"
                    onChange={handleInputChange}
                    placeholder="e.g., 25"
                    type="number"
                    value={formData.age}
                  />
                  {errors.age && (
                    <p className="text-sm text-destructive flex items-center gap-1 animate-in fade-in">
                      <XCircle className="w-3 h-3" />
                      {errors.age}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label className="text-base font-semibold" htmlFor="gender">
                    Gender <span className="text-destructive">*</span>
                  </Label>
                  <Select
                    onValueChange={(value: Gender) => handleSelectChange("gender", value)}
                    value={formData.gender}
                  >
                    <SelectTrigger
                      aria-required="true"
                      className={`h-12 text-base ${errors.gender ? "border-destructive" : ""}`}
                      id="gender"
                    >
                      <SelectValue placeholder="Select gender" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="male">Male</SelectItem>
                      <SelectItem value="female">Female</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-base font-semibold" htmlFor="height">
                  Height (cm) <span className="text-destructive">*</span>
                </Label>
                <Input
                  aria-required="true"
                  className={`h-12 text-base ${errors.height ? "border-destructive" : ""}`}
                  id="height"
                  onChange={handleInputChange}
                  placeholder="e.g., 175"
                  type="number"
                  value={formData.height}
                />
                {errors.height && (
                  <p className="text-sm text-destructive flex items-center gap-1">
                    <XCircle className="w-3 h-3" />
                    {errors.height}
                  </p>
                )}
              </div>

              <Button
                className="w-full h-12 text-base font-semibold mt-8"
                disabled={!isStepValid}
                onClick={handleNext}
                type="button"
              >
                Continue <ArrowRight className="ml-2 w-5 h-5" />
              </Button>
            </div>
          )}

          {step === 2 && (
            <div className="space-y-6 animate-in fade-in slide-in-from-right-4 duration-300">
              <Alert className="border-primary/20 bg-primary/5">
                <Activity className="h-4 w-4 text-primary" />
                <AlertDescription className="text-sm">
                  Accurate measurements help us track your progress effectively
                </AlertDescription>
              </Alert>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label className="text-base font-semibold" htmlFor="weight">
                    Weight (kg) <span className="text-destructive">*</span>
                  </Label>
                  <Input
                    aria-required="true"
                    className={`h-12 text-base ${errors.weight ? "border-destructive" : ""}`}
                    id="weight"
                    onChange={handleInputChange}
                    placeholder="e.g., 70.0"
                    step="0.1"
                    type="number"
                    value={formData.weight}
                  />
                  {errors.weight && (
                    <p className="text-sm text-destructive flex items-center gap-1">
                      <XCircle className="w-3 h-3" />
                      {errors.weight}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label
                    className="text-base font-semibold flex items-center gap-2"
                    htmlFor="bodyFat"
                  >
                    Body Fat %
                    <Badge className="text-xs" variant="secondary">
                      Optional
                    </Badge>
                  </Label>
                  <Input
                    className="h-12 text-base"
                    id="bodyFat"
                    onChange={handleInputChange}
                    placeholder="e.g., 20.0"
                    step="0.1"
                    type="number"
                    value={formData.bodyFat}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label
                    className="text-base font-semibold flex items-center gap-2"
                    htmlFor="waist"
                  >
                    Waist (cm)
                    <Badge className="text-xs" variant="secondary">
                      Optional
                    </Badge>
                  </Label>
                  <Input
                    className="h-12 text-base"
                    id="waist"
                    onChange={handleInputChange}
                    placeholder="e.g., 80.0"
                    step="0.1"
                    type="number"
                    value={formData.waist}
                  />
                </div>

                <div className="space-y-2">
                  <Label className="text-base font-semibold flex items-center gap-2" htmlFor="hip">
                    Hip (cm)
                    <Badge className="text-xs" variant="secondary">
                      Optional
                    </Badge>
                  </Label>
                  <Input
                    className="h-12 text-base"
                    id="hip"
                    onChange={handleInputChange}
                    placeholder="e.g., 95.0"
                    step="0.1"
                    type="number"
                    value={formData.hip}
                  />
                </div>
              </div>

              {formData.weight && formData.height && (
                <div className="p-6 bg-gradient-to-br from-primary/10 to-primary/5 rounded-xl border-2 border-primary/20 animate-in fade-in slide-in-from-bottom-2">
                  <div className="flex items-center justify-between mb-3">
                    <p className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">
                      Your Current BMI
                    </p>
                    <TrendingUp className="w-5 h-5 text-primary" />
                  </div>
                  <div className="flex items-baseline gap-3">
                    <p className="text-5xl font-bold text-primary">{calculateBMI()}</p>
                    <p
                      className={`text-lg font-semibold ${
                        getBMICategory(parseFloat(calculateBMI())).color
                      }`}
                    >
                      {getBMICategory(parseFloat(calculateBMI())).label}
                    </p>
                  </div>
                  <p className="text-xs text-muted-foreground mt-2">
                    Based on your height and weight measurements
                  </p>
                </div>
              )}

              <div className="flex gap-3 mt-8">
                <Button
                  className="flex-1 h-12 text-base font-semibold"
                  onClick={handleBack}
                  type="button"
                  variant="outline"
                >
                  <ArrowLeft className="mr-2 w-5 h-5" /> Back
                </Button>
                <Button
                  className="flex-1 h-12 text-base font-semibold"
                  disabled={!isStepValid}
                  onClick={handleNext}
                  type="button"
                >
                  Continue <ArrowRight className="ml-2 w-5 h-5" />
                </Button>
              </div>
            </div>
          )}

          {step === 3 && (
            <div className="space-y-6 animate-in fade-in slide-in-from-right-4 duration-300">
              <Alert className="border-primary/20 bg-primary/5">
                <Target className="h-4 w-4 text-primary" />
                <AlertDescription className="text-sm">
                  Set realistic goals to stay motivated throughout your journey
                </AlertDescription>
              </Alert>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label className="text-base font-semibold" htmlFor="targetWeight">
                    Target Weight (kg) <span className="text-destructive">*</span>
                  </Label>
                  <Input
                    aria-required="true"
                    className={`h-12 text-base ${errors.targetWeight ? "border-destructive" : ""}`}
                    id="targetWeight"
                    onChange={handleInputChange}
                    placeholder="e.g., 65.0"
                    step="0.1"
                    type="number"
                    value={formData.targetWeight}
                  />
                  {formData.weight && formData.targetWeight && (
                    <p className="text-xs text-muted-foreground">
                      {parseFloat(formData.weight) - parseFloat(formData.targetWeight) > 0
                        ? `Goal: Lose ${(
                            parseFloat(formData.weight) - parseFloat(formData.targetWeight)
                          ).toFixed(1)} kg`
                        : `Goal: Gain ${(
                            parseFloat(formData.targetWeight) - parseFloat(formData.weight)
                          ).toFixed(1)} kg`}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label
                    className="text-base font-semibold flex items-center gap-2"
                    htmlFor="targetBodyFat"
                  >
                    Target Body Fat %
                    <Badge className="text-xs" variant="secondary">
                      Optional
                    </Badge>
                  </Label>
                  <Input
                    className="h-12 text-base"
                    id="targetBodyFat"
                    onChange={handleInputChange}
                    placeholder="e.g., 15.0"
                    step="0.1"
                    type="number"
                    value={formData.targetBodyFat}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-base font-semibold" htmlFor="timeline">
                  Timeline (days) <span className="text-destructive">*</span>
                </Label>
                <Input
                  aria-required="true"
                  className={`h-12 text-base ${errors.timeline ? "border-destructive" : ""}`}
                  id="timeline"
                  onChange={handleInputChange}
                  placeholder="e.g., 90"
                  type="number"
                  value={formData.timeline}
                />
                {formData.timeline && (
                  <p className="text-xs text-muted-foreground">
                    Approximately {Math.floor(parseInt(formData.timeline, 10) / 7)} weeks or{" "}
                    {Math.floor(parseInt(formData.timeline, 10) / 30)} months
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label className="text-base font-semibold" htmlFor="activityLevel">
                  Activity Level <span className="text-destructive">*</span>
                </Label>
                <Select
                  onValueChange={(value: ActivityLevel) =>
                    handleSelectChange("activityLevel", value)
                  }
                  value={formData.activityLevel}
                >
                  <SelectTrigger
                    aria-required="true"
                    className="py-2 text-base text-left"
                    id="activityLevel"
                    style={{ height: "auto" }}
                  >
                    <SelectValue placeholder="Select your activity level" />
                  </SelectTrigger>
                  <SelectContent>
                    {[
                      {
                        description: "Little to no exercise",
                        label: "Sedentary",
                        value: "sedentary",
                      },
                      {
                        description: "Exercise 1-3 days/week",
                        label: "Lightly Active",
                        value: "lightly_active",
                      },
                      {
                        description: "Exercise 3-5 days/week",
                        label: "Moderately Active",
                        value: "moderately_active",
                      },
                      {
                        description: "Exercise 6-7 days/week",
                        label: "Very Active",
                        value: "very_active",
                      },
                    ].map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="py-2">
                          <p className="font-semibold">{option.label}</p>
                          <p className="text-sm text-muted-foreground">{option.description}</p>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {formData.activityLevel && (
                <div className="p-6 bg-gradient-to-br from-primary/10 to-primary/5 rounded-xl border-2 border-primary/20 animate-in fade-in slide-in-from-bottom-2">
                  <div className="flex items-center justify-between mb-3">
                    <p className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">
                      Recommended Daily Calories
                    </p>
                    <Activity className="w-5 h-5 text-primary" />
                  </div>
                  <div className="flex items-baseline gap-3">
                    <p className="text-5xl font-bold text-primary">{calculateCalories()}</p>
                    <p className="text-lg font-semibold text-muted-foreground">kcal</p>
                  </div>
                  <p className="text-xs text-muted-foreground mt-2">
                    Based on Harris-Benedict equation and your activity level
                  </p>
                </div>
              )}

              <div className="flex gap-3 mt-8">
                <Button
                  className="flex-1 h-12 text-base font-semibold"
                  disabled={loading}
                  onClick={handleBack}
                  type="button"
                  variant="outline"
                >
                  <ArrowLeft className="mr-2 w-5 h-5" /> Back
                </Button>
                <Button
                  className="flex-1 h-12 text-base font-semibold"
                  disabled={loading || !isStepValid}
                  onClick={handleSubmit}
                  type="submit"
                >
                  {loading ? (
                    <>
                      <div className="mr-2 h-5 w-5 animate-spin rounded-full border-2 border-background border-t-transparent" />
                      Creating...
                    </>
                  ) : (
                    <>
                      Complete Setup <CheckCircle2 className="ml-2 w-5 h-5" />
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}
          {step === 4 && (
            <div className="space-y-6 text-center animate-in fade-in duration-500">
              <CheckCircle2 className="mx-auto h-16 w-16 text-green-500" />
              <Alert className="border-green-500/20 bg-green-500/5">
                <AlertTitle className="text-lg font-semibold text-green-600">
                  You're All Set!
                </AlertTitle>
                <AlertDescription>
                  Your personalized fitness plan is ready. Let's start your journey to a healthier
                  you.
                </AlertDescription>
              </Alert>
              <Button
                className="w-full h-12 text-base font-semibold"
                onClick={() => alert("Redirecting to dashboard...")}
                type="button"
              >
                Go to Dashboard
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default Onboarding;
